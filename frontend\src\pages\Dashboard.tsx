import React from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Container,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Dns as CpuIcon,
  Refresh,
} from '@mui/icons-material';
import { RootState } from '../types/store';
import { useSystemMetrics } from '../hooks/useSystemMetrics';
import SystemMetricsCard from '../components/Dashboard/SystemMetricsCard';
import SystemUsageChart from '../components/Dashboard/SystemUsageChart';
import SystemOverview from '../components/Dashboard/SystemOverview';

const Dashboard = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  const {
    metrics,
    historicalMetrics,
    isLoading,
    error,
    refreshMetrics,
  } = useSystemMetrics({
    refreshInterval: 30000,
    autoRefresh: true,
  });

  if (isLoading && !metrics) {
    return (
      <Container maxWidth="xl">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          <CircularProgress size={60} />
          <Typography variant="h6" color="textSecondary">
            Loading system metrics...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mt: 4 }}>
          <Alert
            severity="error"
            action={
              <Box sx={{ cursor: 'pointer' }} onClick={refreshMetrics}>
                <Refresh />
              </Box>
            }
          >
            {error}
          </Alert>
        </Box>
      </Container>
    );
  }

  if (!metrics) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mt: 4 }}>
          <Alert severity="warning">
            No system metrics available. Please check your connection.
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Fade in timeout={800}>
        <Box>
          {/* Header */}
          <Box
            sx={{
              mb: 4,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}20 0%, ${theme.palette.secondary.main}20 100%)`,
              borderRadius: 3,
              p: 4,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Typography
              variant="h3"
              fontWeight={700}
              color="textPrimary"
              gutterBottom
            >
              Welcome back, {user?.username}! 👋
            </Typography>
            <Typography
              variant="h6"
              color="textSecondary"
              fontWeight={400}
              sx={{ mb: 2 }}
            >
              Here's what's happening with your virtual lab environment
            </Typography>
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ opacity: 0.8 }}
            >
              Last updated: {new Date(metrics.timestamp).toLocaleString()}
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* System Overview */}
            <Grid item xs={12}>
              <SystemOverview systemInfo={metrics} />
            </Grid>

            {/* Resource Usage Cards */}
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="CPU Usage"
                value={metrics.resources.cpu.usage_percentage}
                unit="%"
                icon={<CpuIcon sx={{ fontSize: 32 }} />}
                color="primary"
                status={
                  metrics.resources.cpu.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="up"
                trendValue={2.3}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="Memory Usage"
                value={metrics.resources.memory.usage_percentage}
                unit="%"
                icon={<MemoryIcon sx={{ fontSize: 32 }} />}
                color="secondary"
                status={
                  metrics.resources.memory.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="down"
                trendValue={-1.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="Disk Usage"
                value={metrics.resources.disk.usage_percentage}
                unit="%"
                icon={<StorageIcon sx={{ fontSize: 32 }} />}
                color="warning"
                status={
                  metrics.resources.disk.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="flat"
                trendValue={0.1}
              />
            </Grid>

            {/* Historical Usage Chart */}
            {historicalMetrics && (
              <Grid item xs={12}>
                <SystemUsageChart
                  title="System Resource Usage (Last 24 Hours)"
                  data={{
                    timestamps: historicalMetrics.timestamps,
                    cpu_usage: historicalMetrics.metrics.cpu_usage,
                    memory_usage: historicalMetrics.metrics.memory_usage,
                    disk_usage: historicalMetrics.metrics.disk_usage,
                  }}
                  height={400}
                />
              </Grid>
            )}
          </Grid>
        </Box>
      </Fade>
    </Container>
  );
};

export default Dashboard;
