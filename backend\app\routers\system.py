from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from ..models.user import User, UserRole
from ..routers.auth import get_current_user
from ..services.proxmox import ProxmoxService
import asyncio

router = APIRouter(prefix="/system", tags=["system"])

@router.get("/nodes")
async def get_nodes(
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """Get list of available Proxmox nodes."""
    # Only admins and instructors can view system information
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view system information"
        )
    
    proxmox = ProxmoxService()
    try:
        nodes = await proxmox.get_nodes()
        return nodes
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get nodes: {str(e)}"
        )

@router.get("/cluster-status")
async def get_cluster_status(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get overall cluster status and health."""
    # Only admins and instructors can view system information
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view system information"
        )
    
    proxmox = ProxmoxService()
    try:
        status = await proxmox.get_cluster_status()
        return status
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cluster status: {str(e)}"
        )

@router.get("/proxmox-info")
async def get_proxmox_info(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get Proxmox server information."""
    # Only admins can view detailed Proxmox information
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    
    proxmox = ProxmoxService()
    try:
        # Get version information
        version = proxmox.proxmox.version.get()
        
        # Get cluster status
        cluster_status = await proxmox.get_cluster_status()
        
        return {
            "version": version.get("version", "Unknown"),
            "release": version.get("release", "Unknown"),
            "cluster": cluster_status,
            "connection_status": "connected"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get Proxmox information: {str(e)}"
        )

@router.get("/metrics")
async def get_system_metrics(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get comprehensive system metrics including resource usage."""
    # Only admins and instructors can view system metrics
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view system metrics"
        )

    proxmox = ProxmoxService()
    try:
        # Get cluster status and nodes
        cluster_status = await proxmox.get_cluster_status()
        nodes = cluster_status.get("nodes", [])

        # Calculate aggregate metrics
        total_cpu_usage = 0
        total_memory_usage = 0
        total_disk_usage = 0
        total_memory = 0
        total_disk = 0
        online_nodes = 0

        for node in nodes:
            if node.get("status") == "online":
                online_nodes += 1
                total_cpu_usage += node.get("cpu_usage", 0)
                total_memory_usage += node.get("memory_usage", 0)
                total_disk_usage += node.get("disk_usage", 0)

        # Calculate averages
        avg_cpu_usage = total_cpu_usage / max(online_nodes, 1)
        avg_memory_usage = total_memory_usage / max(online_nodes, 1)
        avg_disk_usage = total_disk_usage / max(online_nodes, 1)

        # Get VM statistics
        vm_stats = await proxmox.get_vm_statistics()

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "cluster": {
                "total_nodes": len(nodes),
                "online_nodes": online_nodes,
                "health": cluster_status.get("cluster_health", "unknown")
            },
            "resources": {
                "cpu": {
                    "usage_percentage": round(avg_cpu_usage, 2),
                    "status": "normal" if avg_cpu_usage < 80 else "high"
                },
                "memory": {
                    "usage_percentage": round(avg_memory_usage, 2),
                    "status": "normal" if avg_memory_usage < 80 else "high"
                },
                "disk": {
                    "usage_percentage": round(avg_disk_usage, 2),
                    "status": "normal" if avg_disk_usage < 80 else "high"
                }
            },
            "virtual_machines": vm_stats,
            "nodes": nodes
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get system metrics: {str(e)}"
        )

@router.get("/metrics/historical")
async def get_historical_metrics(
    current_user: User = Depends(get_current_user),
    hours: int = 24
) -> Dict[str, Any]:
    """Get historical system metrics for the specified time period."""
    # Only admins and instructors can view historical metrics
    if current_user.role not in [UserRole.ADMIN, UserRole.INSTRUCTOR]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to view historical metrics"
        )

    # For now, return mock historical data
    # In a real implementation, this would query a time-series database
    timestamps = []
    cpu_data = []
    memory_data = []
    disk_data = []

    # Generate mock data for the last 'hours' hours
    import random
    current_time = datetime.utcnow()
    for i in range(hours):
        timestamp = current_time - timedelta(hours=hours-i)
        timestamps.append(timestamp.isoformat())

        # Generate realistic mock data with some variation
        base_cpu = 45 + random.uniform(-15, 15)
        base_memory = 60 + random.uniform(-20, 20)
        base_disk = 35 + random.uniform(-10, 10)

        cpu_data.append(max(0, min(100, base_cpu)))
        memory_data.append(max(0, min(100, base_memory)))
        disk_data.append(max(0, min(100, base_disk)))

    return {
        "period_hours": hours,
        "timestamps": timestamps,
        "metrics": {
            "cpu_usage": cpu_data,
            "memory_usage": memory_data,
            "disk_usage": disk_data
        }
    }

@router.get("/health")
async def system_health_check() -> Dict[str, Any]:
    """Comprehensive system health check."""
    health_status = {
        "status": "healthy",
        "components": {
            "api": "up",
            "database": "unknown",
            "proxmox": "unknown"
        },
        "timestamp": None
    }

    # Test Proxmox connection
    try:
        proxmox = ProxmoxService()
        version = proxmox.proxmox.version.get()
        health_status["components"]["proxmox"] = "up"
        health_status["proxmox_version"] = version.get("version", "Unknown")
    except Exception as e:
        health_status["components"]["proxmox"] = "down"
        health_status["proxmox_error"] = str(e)
        health_status["status"] = "degraded"

    # Add timestamp
    health_status["timestamp"] = datetime.utcnow().isoformat()

    return health_status
